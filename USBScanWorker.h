#ifndef USBSCANWORKER_H
#define USBSCANWORKER_H

#include <QObject>
#include <QTimer>
#include <QStringList>
#include <QStorageInfo>
#include <QMutex>
#include <QDir>
#include <QFile>

#ifdef Q_OS_WIN
#include <windows.h>
#endif

/**
 * @brief USB扫描工作线程
 * 
 * 在独立线程中执行USB设备扫描，避免阻塞主线程
 */
class USBScanWorker : public QObject
{
    Q_OBJECT

public:
    explicit USBScanWorker(QObject *parent = nullptr);
    ~USBScanWorker();

    // 启动/停止扫描
    void startScanning();
    void stopScanning();
    
    // 设置扫描间隔（毫秒）
    void setScanInterval(int intervalMs);

public slots:
    // 执行扫描工作
    void doScan();

signals:
    // 扫描完成信号
    void scanCompleted(const QStringList &usbDevices);
    
    // 工作线程状态信号
    void scanStarted();
    void scanStopped();

private:
    // 获取当前USB设备列表
    QStringList getCurrentUSBDevices();
    
    // 检查USB设备是否包含白名单
    bool checkForWhiteList(const QString &usbPath);

private:
    QTimer *m_scanTimer;
    QMutex m_mutex;
    bool m_isScanning;
    int m_scanInterval;
    
    // 配置参数
    static const QString WHITE_LIST_FILE_NAME;
};

#endif // USBSCANWORKER_H
