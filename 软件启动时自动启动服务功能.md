# 软件启动时自动启动服务功能

## 🎯 功能目标
在显示器管控程序启动时，自动检查并启动Windows服务，确保服务始终处于运行状态。

## ✅ 已实现的功能

### 1. **自动启动服务逻辑**

#### **新增函数：autoStartServiceOnStartup()**
```cpp
void CDlgMain_MonitorWhiteCtrlProgram::autoStartServiceOnStartup()
{
    if (!m_serviceManager) {
        qWarning() << "ServiceManager not initialized";
        return;
    }
    
    qDebug() << "Checking service auto-start on software startup...";
    
    // 检查服务是否已安装
    if (!m_serviceManager->isServiceInstalled()) {
        qDebug() << "Service not installed, attempting to install...";
        
        // 检查是否有管理员权限
        if (!m_serviceManager->isRunningAsAdmin()) {
            qDebug() << "No admin privileges, skipping service installation";
            WriteLog("Service installation skipped: No admin privileges");
            return;
        }
        
        // 尝试安装服务
        if (m_serviceManager->installService()) {
            qDebug() << "Service installed successfully";
            WriteLog("Service installed successfully");
        } else {
            qWarning() << "Failed to install service";
            WriteLog("Failed to install service");
            return;
        }
    }
    
    // 检查服务是否正在运行
    if (m_serviceManager->isServiceRunning()) {
        qDebug() << "Service is already running";
        WriteLog("Service is already running");
        return;
    }
    
    // 尝试启动服务
    qDebug() << "Attempting to start service...";
    WriteLog("Attempting to start service on software startup");
    
    if (m_serviceManager->startService()) {
        qDebug() << "Service started successfully";
        WriteLog("Service started successfully");
        
        // 如果还没有启用自启动，记录信息
        if (!m_serviceManager->isAutoStartEnabled()) {
            qDebug() << "Auto-start not enabled, service will start with software";
            WriteLog("Auto-start not enabled, service will start with software");
        }
    } else {
        qWarning() << "Failed to start service";
        WriteLog("Failed to start service");
    }
}
```

### 2. **集成到初始化流程**

#### **在initServiceManager()中调用**
```cpp
void CDlgMain_MonitorWhiteCtrlProgram::initServiceManager()
{
    m_serviceManager = new ServiceManager(this);

    qDebug() << "ServiceManager initialized";
    qDebug() << "Service status:" << m_serviceManager->getServiceStatusString();
    qDebug() << "Auto start:" << (m_serviceManager->isAutoStartEnabled() ? "Enabled" : "Disabled");

    // 软件启动时自动启动服务
    autoStartServiceOnStartup();
}
```

### 3. **智能服务管理策略**

#### **多层检查机制**
1. **服务安装检查** - 如果服务未安装，尝试自动安装
2. **权限检查** - 只在有管理员权限时尝试安装服务
3. **运行状态检查** - 避免重复启动已运行的服务
4. **启动尝试** - 自动启动未运行的服务

#### **权限处理策略**
- **有管理员权限** - 自动安装和启动服务
- **无管理员权限** - 跳过服务安装，记录日志
- **服务已安装** - 直接尝试启动，无需管理员权限

## 🔧 技术实现

### 1. **服务状态检查**
```cpp
// 检查服务是否已安装
if (!m_serviceManager->isServiceInstalled()) {
    // 尝试安装服务
}

// 检查服务是否正在运行
if (m_serviceManager->isServiceRunning()) {
    // 服务已运行，无需启动
    return;
}
```

### 2. **权限检查**
```cpp
// 检查是否有管理员权限
if (!m_serviceManager->isRunningAsAdmin()) {
    qDebug() << "No admin privileges, skipping service installation";
    WriteLog("Service installation skipped: No admin privileges");
    return;
}
```

### 3. **日志记录**
```cpp
// 记录所有关键操作到日志文件
WriteLog("Attempting to start service on software startup");
WriteLog("Service started successfully");
WriteLog("Service installation skipped: No admin privileges");
```

## 📋 运行流程

### **程序启动时的服务检查流程**

```
程序启动
    ↓
初始化ServiceManager
    ↓
调用autoStartServiceOnStartup()
    ↓
检查服务是否已安装？
    ├─ 否 → 检查管理员权限？
    │        ├─ 是 → 安装服务 → 启动服务
    │        └─ 否 → 跳过安装，记录日志
    └─ 是 → 检查服务是否运行？
             ├─ 是 → 记录"服务已运行"
             └─ 否 → 启动服务
```

### **不同场景的处理**

#### **场景1：首次运行（管理员权限）**
```
Debug: Service not installed, attempting to install...
Debug: Service installed successfully
Debug: Attempting to start service...
Debug: Service started successfully
```

#### **场景2：首次运行（普通用户）**
```
Debug: Service not installed, attempting to install...
Debug: No admin privileges, skipping service installation
```

#### **场景3：服务已安装但未运行**
```
Debug: Attempting to start service...
Debug: Service started successfully
```

#### **场景4：服务已运行**
```
Debug: Service is already running
```

## 🎯 功能优势

### 1. **自动化管理**
- **无需手动操作** - 程序启动时自动处理服务
- **智能检测** - 自动检测服务状态并采取相应行动
- **权限适应** - 根据权限情况智能处理

### 2. **健壮性**
- **多重检查** - 避免重复安装或启动
- **错误处理** - 优雅处理各种异常情况
- **日志记录** - 完整记录所有操作

### 3. **用户友好**
- **透明操作** - 用户无需关心服务管理细节
- **权限提示** - 清晰的权限相关信息
- **状态反馈** - 通过日志了解服务状态

### 4. **开发友好**
- **调试信息** - 详细的英文调试输出
- **模块化** - 独立的函数便于维护
- **可配置** - 可以根据需要调整策略

## 📊 调试输出示例

### **完整的启动调试输出**
```
Debug: ========================================
Debug: Monitor Control Program Started
Debug: Version: 1.0
Debug: Build Time: Dec  7 2024 22:30:00
Debug: ========================================
Debug: ServiceManager initialized
Debug: Executable path: E:/Work/Project/.../001_MonitorWhiteCtrlProgram.exe
Debug: Service status: Not Installed
Debug: Auto start: Disabled
Debug: Checking service auto-start on software startup...
Debug: Service not installed, attempting to install...
Debug: Service installed successfully
Debug: Attempting to start service...
Debug: Service started successfully
Debug: Auto-start not enabled, service will start with software
```

### **日志文件记录**
```
[2024-12-07 22:30:00.123] Monitor Control Program Started
[2024-12-07 22:30:00.456] Attempting to start service on software startup
[2024-12-07 22:30:01.789] Service installed successfully
[2024-12-07 22:30:02.012] Service started successfully
[2024-12-07 22:30:02.345] Auto-start not enabled, service will start with software
```

## 🔧 配置选项

### **可调整的行为**
1. **是否自动安装服务** - 当前：有管理员权限时自动安装
2. **是否自动启动服务** - 当前：总是尝试启动
3. **权限不足时的行为** - 当前：跳过并记录日志
4. **服务已运行时的行为** - 当前：跳过并记录日志

### **未来扩展可能**
1. **用户确认对话框** - 询问用户是否要安装/启动服务
2. **配置文件控制** - 通过配置文件控制自动启动行为
3. **重试机制** - 启动失败时的重试逻辑
4. **服务健康检查** - 定期检查服务状态

## ✅ 总结

### **实现的核心功能**
1. **✅ 自动服务检查** - 程序启动时自动检查服务状态
2. **✅ 智能安装** - 根据权限情况智能安装服务
3. **✅ 自动启动** - 自动启动未运行的服务
4. **✅ 完整日志** - 记录所有操作到日志文件
5. **✅ 错误处理** - 优雅处理各种异常情况

### **技术优势**
- **模块化设计** - 独立的函数便于维护和测试
- **权限感知** - 智能处理不同权限级别
- **状态检查** - 避免重复操作
- **日志完整** - 便于问题诊断和用户支持

**🎉 现在您的显示器管控程序具备了智能的服务自动启动功能！程序启动时会自动确保服务处于运行状态！** 🚀
