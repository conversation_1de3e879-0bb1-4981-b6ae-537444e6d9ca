/****************************************************************************
** Meta object code from reading C++ file 'USBScanWorker.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../USBScanWorker.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'USBScanWorker.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_USBScanWorker_t {
    QByteArrayData data[7];
    char stringdata0[71];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_USBScanWorker_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_USBScanWorker_t qt_meta_stringdata_USBScanWorker = {
    {
QT_MOC_LITERAL(0, 0, 13), // "USBScanWorker"
QT_MOC_LITERAL(1, 14, 13), // "scanCompleted"
QT_MOC_LITERAL(2, 28, 0), // ""
QT_MOC_LITERAL(3, 29, 10), // "usbDevices"
QT_MOC_LITERAL(4, 40, 11), // "scanStarted"
QT_MOC_LITERAL(5, 52, 11), // "scanStopped"
QT_MOC_LITERAL(6, 64, 6) // "doScan"

    },
    "USBScanWorker\0scanCompleted\0\0usbDevices\0"
    "scanStarted\0scanStopped\0doScan"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_USBScanWorker[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   34,    2, 0x06 /* Public */,
       4,    0,   37,    2, 0x06 /* Public */,
       5,    0,   38,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       6,    0,   39,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::QStringList,    3,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,

       0        // eod
};

void USBScanWorker::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        USBScanWorker *_t = static_cast<USBScanWorker *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->scanCompleted((*reinterpret_cast< const QStringList(*)>(_a[1]))); break;
        case 1: _t->scanStarted(); break;
        case 2: _t->scanStopped(); break;
        case 3: _t->doScan(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (USBScanWorker::*_t)(const QStringList & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&USBScanWorker::scanCompleted)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (USBScanWorker::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&USBScanWorker::scanStarted)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (USBScanWorker::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&USBScanWorker::scanStopped)) {
                *result = 2;
                return;
            }
        }
    }
}

const QMetaObject USBScanWorker::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_USBScanWorker.data,
      qt_meta_data_USBScanWorker,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *USBScanWorker::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *USBScanWorker::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_USBScanWorker.stringdata0))
        return static_cast<void*>(const_cast< USBScanWorker*>(this));
    return QObject::qt_metacast(_clname);
}

int USBScanWorker::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void USBScanWorker::scanCompleted(const QStringList & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void USBScanWorker::scanStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 1, Q_NULLPTR);
}

// SIGNAL 2
void USBScanWorker::scanStopped()
{
    QMetaObject::activate(this, &staticMetaObject, 2, Q_NULLPTR);
}
QT_END_MOC_NAMESPACE
