#ifndef WHITELISTMANAGER_H
#define WHITELISTMANAGER_H

#include <QObject>
#include <QStringList>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QDir>
#include <QStandardPaths>
#include <QMutex>
#include <QDateTime>
#include "EDIDManager.h"

/**
 * @brief 显示器白名单管理器
 * 
 * 管理显示器白名单的存储、加载、同步等功能
 * 支持从USB Key读取白名单数据
 */
class WhiteListManager : public QObject
{
    Q_OBJECT

public:
    // 白名单条目结构
    struct WhiteListEntry {
        QString manufacturer;    // 制造商
        QString productCode;     // 产品代码
        QString serialNumber;    // 序列号
        QString deviceName;      // 设备名称
        QString addTime;         // 添加时间
        QString source;          // 来源（本地/USB Key）
        
        WhiteListEntry() {}
        WhiteListEntry(const DisplayInfo &info, const QString &src = "本地") {
            manufacturer = info.manufacturer;
            productCode = info.productCode;
            serialNumber = info.serialNumber;
            deviceName = info.deviceName;
            addTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
            source = src;
        }
        
        // 生成唯一标识符
        QString getUniqueId() const {
            return QString("%1_%2_%3").arg(manufacturer).arg(productCode).arg(serialNumber);
        }
        
        // 转换为JSON对象
        QJsonObject toJson() const {
            QJsonObject obj;
            obj["manufacturer"] = manufacturer;
            obj["productCode"] = productCode;
            obj["serialNumber"] = serialNumber;
            obj["deviceName"] = deviceName;
            obj["addTime"] = addTime;
            obj["source"] = source;
            return obj;
        }
        
        // 从JSON对象创建
        static WhiteListEntry fromJson(const QJsonObject &obj) {
            WhiteListEntry entry;
            entry.manufacturer = obj["manufacturer"].toString();
            entry.productCode = obj["productCode"].toString();
            entry.serialNumber = obj["serialNumber"].toString();
            entry.deviceName = obj["deviceName"].toString();
            entry.addTime = obj["addTime"].toString();
            entry.source = obj["source"].toString();
            return entry;
        }
    };

public:
    explicit WhiteListManager(QObject *parent = nullptr);
    ~WhiteListManager();

    // 白名单操作
    bool addToWhiteList(const DisplayInfo &displayInfo, const QString &source = "本地");
    bool removeFromWhiteList(const QString &uniqueId);
    void clearWhiteList();
    
    // 查询操作
    bool isInWhiteList(const DisplayInfo &displayInfo) const;
    bool isInWhiteList(const QString &manufacturer, const QString &productCode, const QString &serialNumber) const;
    QList<WhiteListEntry> getAllEntries() const;
    int getWhiteListCount() const;
    
    // 文件操作
    bool saveToFile();
    bool loadFromFile();
    QString getWhiteListFilePath() const;
    
    // USB Key操作
    bool syncFromUSBKey(const QString &usbPath);
    bool exportToUSBKey(const QString &usbPath);
    QStringList findUSBKeys() const;
    
    // 统计信息
    int getLocalCount() const;
    int getUSBCount() const;
    QString getLastSyncTime() const;

signals:
    void whiteListUpdated(int count);
    void whiteListCleared();
    void syncCompleted(bool success, const QString &message);

private:
    bool parseUSBKeyData(const QString &filePath, QList<WhiteListEntry> &entries);
    bool validateWhiteListEntry(const WhiteListEntry &entry) const;
    QString generateBackupFileName() const;
    void createBackup();

private:
    QList<WhiteListEntry> m_whiteList;
    mutable QMutex m_mutex;
    QString m_whiteListFile;
    QString m_lastSyncTime;
    
    // 配置参数
    static const QString WHITE_LIST_FILE_NAME;
    static const QString USB_WHITE_LIST_FILE_NAME;
    static const int MAX_BACKUP_FILES;
};

#endif // WHITELISTMANAGER_H
