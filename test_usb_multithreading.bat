@echo off
echo ========================================
echo 测试USB设备监控器多线程功能
echo ========================================
echo.

echo 1. 检查当前USB设备状态...
echo.

echo 检查可移动磁盘:
wmic logicaldisk where drivetype=2 get size,freespace,caption
echo.

echo 2. 启动程序测试多线程USB监控...
echo.
echo 程序将使用多线程进行USB设备扫描：
echo - 扫描间隔：1秒
echo - 工作线程：独立线程执行扫描
echo - 主线程：处理UI和事件
echo.

start "" "E:\Work\Project\001_MonitorWhiteCtrlProgram\001_Code\build-001_MonitorWhiteCtrlProgram-Desktop_Qt_5_6_3_MinGW_32bit-Debug\debug\001_MonitorWhiteCtrlProgram.exe"

echo 3. 等待程序启动...
timeout /t 5 /nobreak >nul

echo.
echo 4. 测试项目：
echo.
echo ✓ 多线程扫描功能测试：
echo   - 插入USB设备，观察1秒内检测
echo   - 移除USB设备，观察1秒内检测
echo   - 检查日志中的线程相关信息
echo.
echo ✓ 预期日志输出：
echo   - "USB device monitor initialized with multithreading support"
echo   - "Starting USB device monitoring with multithreading (1-second interval)"
echo   - "USB scan thread started"
echo   - "USB scan worker started"
echo   - "Starting USB scanning with 1000ms interval"
echo.
echo ✓ USB设备变化检测：
echo   - "Detected new USB device: [设备路径]"
echo   - "Detected USB device removal: [设备路径]"
echo   - "Found USB key with whitelist: [设备路径]" (如果包含白名单)
echo.

echo 5. 请执行以下测试步骤：
echo.
echo 步骤1: 插入一个USB设备
echo 步骤2: 等待1-2秒，观察检测结果
echo 步骤3: 移除USB设备
echo 步骤4: 等待1-2秒，观察检测结果
echo 步骤5: 如果有包含白名单的USB Key，测试自动同步功能
echo.

echo 6. 测试完成后按任意键查看日志...
pause

echo.
echo 7. 检查日志文件...
echo.

set LOG_FILE="E:\Work\Project\001_MonitorWhiteCtrlProgram\001_Code\build-001_MonitorWhiteCtrlProgram-Desktop_Qt_5_6_3_MinGW_32bit-Debug\debug\Log\2025-05-29_MonitorCtrl.log"

if exist %LOG_FILE% (
    echo 显示最新日志条目（多线程相关）:
    echo.
    powershell -Command "Get-Content %LOG_FILE% | Select-String -Pattern 'thread|Thread|scan|USB|multithreading' | Select-Object -Last 20"
    echo.
    echo 显示所有最新日志条目:
    echo.
    powershell -Command "Get-Content %LOG_FILE% | Select-Object -Last 15"
) else (
    echo 日志文件不存在
)

echo.
echo 8. 性能验证...
echo.

echo 检查进程线程数:
powershell -Command "Get-Process | Where-Object {$_.ProcessName -eq '001_MonitorWhiteCtrlProgram'} | Select-Object ProcessName, Threads"

echo.
echo ========================================
echo 测试完成
echo ========================================
echo.
echo 验证要点：
echo 1. 程序启动时创建了USB扫描工作线程
echo 2. 扫描间隔为1秒（1000ms）
echo 3. USB设备变化能在1秒内被检测到
echo 4. 主线程不被扫描操作阻塞
echo 5. 线程安全的设备列表管理
echo 6. 工作线程正确启动和停止
echo.
pause
