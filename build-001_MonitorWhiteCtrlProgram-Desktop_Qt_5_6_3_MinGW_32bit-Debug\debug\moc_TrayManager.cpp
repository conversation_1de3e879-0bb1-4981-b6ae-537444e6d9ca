/****************************************************************************
** Meta object code from reading C++ file 'TrayManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../TrayManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'TrayManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_TrayManager_t {
    QByteArrayData data[19];
    char stringdata0[310];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_TrayManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_TrayManager_t qt_meta_stringdata_TrayManager = {
    {
QT_MOC_LITERAL(0, 0, 11), // "TrayManager"
QT_MOC_LITERAL(1, 12, 23), // "clearWhiteListRequested"
QT_MOC_LITERAL(2, 36, 0), // ""
QT_MOC_LITERAL(3, 37, 22), // "syncWhiteListRequested"
QT_MOC_LITERAL(4, 60, 21), // "startServiceRequested"
QT_MOC_LITERAL(5, 82, 25), // "uninstallServiceRequested"
QT_MOC_LITERAL(6, 108, 19), // "onTrayIconActivated"
QT_MOC_LITERAL(7, 128, 33), // "QSystemTrayIcon::ActivationRe..."
QT_MOC_LITERAL(8, 162, 6), // "reason"
QT_MOC_LITERAL(9, 169, 16), // "onShowMainWindow"
QT_MOC_LITERAL(10, 186, 16), // "onHideMainWindow"
QT_MOC_LITERAL(11, 203, 16), // "onClearWhiteList"
QT_MOC_LITERAL(12, 220, 15), // "onSyncWhiteList"
QT_MOC_LITERAL(13, 236, 14), // "onStartService"
QT_MOC_LITERAL(14, 251, 18), // "onUninstallService"
QT_MOC_LITERAL(15, 270, 7), // "onAbout"
QT_MOC_LITERAL(16, 278, 6), // "onExit"
QT_MOC_LITERAL(17, 285, 18), // "onWhiteListUpdated"
QT_MOC_LITERAL(18, 304, 5) // "count"

    },
    "TrayManager\0clearWhiteListRequested\0"
    "\0syncWhiteListRequested\0startServiceRequested\0"
    "uninstallServiceRequested\0onTrayIconActivated\0"
    "QSystemTrayIcon::ActivationReason\0"
    "reason\0onShowMainWindow\0onHideMainWindow\0"
    "onClearWhiteList\0onSyncWhiteList\0"
    "onStartService\0onUninstallService\0"
    "onAbout\0onExit\0onWhiteListUpdated\0"
    "count"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_TrayManager[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   84,    2, 0x06 /* Public */,
       3,    0,   85,    2, 0x06 /* Public */,
       4,    0,   86,    2, 0x06 /* Public */,
       5,    0,   87,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       6,    1,   88,    2, 0x0a /* Public */,
       9,    0,   91,    2, 0x0a /* Public */,
      10,    0,   92,    2, 0x0a /* Public */,
      11,    0,   93,    2, 0x0a /* Public */,
      12,    0,   94,    2, 0x0a /* Public */,
      13,    0,   95,    2, 0x0a /* Public */,
      14,    0,   96,    2, 0x0a /* Public */,
      15,    0,   97,    2, 0x0a /* Public */,
      16,    0,   98,    2, 0x0a /* Public */,
      17,    1,   99,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   18,

       0        // eod
};

void TrayManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        TrayManager *_t = static_cast<TrayManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->clearWhiteListRequested(); break;
        case 1: _t->syncWhiteListRequested(); break;
        case 2: _t->startServiceRequested(); break;
        case 3: _t->uninstallServiceRequested(); break;
        case 4: _t->onTrayIconActivated((*reinterpret_cast< QSystemTrayIcon::ActivationReason(*)>(_a[1]))); break;
        case 5: _t->onShowMainWindow(); break;
        case 6: _t->onHideMainWindow(); break;
        case 7: _t->onClearWhiteList(); break;
        case 8: _t->onSyncWhiteList(); break;
        case 9: _t->onStartService(); break;
        case 10: _t->onUninstallService(); break;
        case 11: _t->onAbout(); break;
        case 12: _t->onExit(); break;
        case 13: _t->onWhiteListUpdated((*reinterpret_cast< int(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (TrayManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&TrayManager::clearWhiteListRequested)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (TrayManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&TrayManager::syncWhiteListRequested)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (TrayManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&TrayManager::startServiceRequested)) {
                *result = 2;
                return;
            }
        }
        {
            typedef void (TrayManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&TrayManager::uninstallServiceRequested)) {
                *result = 3;
                return;
            }
        }
    }
}

const QMetaObject TrayManager::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_TrayManager.data,
      qt_meta_data_TrayManager,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *TrayManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TrayManager::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_TrayManager.stringdata0))
        return static_cast<void*>(const_cast< TrayManager*>(this));
    return QObject::qt_metacast(_clname);
}

int TrayManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 14;
    }
    return _id;
}

// SIGNAL 0
void TrayManager::clearWhiteListRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 0, Q_NULLPTR);
}

// SIGNAL 1
void TrayManager::syncWhiteListRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 1, Q_NULLPTR);
}

// SIGNAL 2
void TrayManager::startServiceRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 2, Q_NULLPTR);
}

// SIGNAL 3
void TrayManager::uninstallServiceRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 3, Q_NULLPTR);
}
QT_END_MOC_NAMESPACE
