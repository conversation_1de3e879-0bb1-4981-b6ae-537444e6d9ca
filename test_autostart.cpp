#include "ServiceManager.h"
#include <QCoreApplication>
#include <QDebug>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "========================================";
    qDebug() << "Testing Auto-Start Functionality";
    qDebug() << "========================================";
    
    ServiceManager serviceManager;
    
    // 检查当前状态
    qDebug() << "Current service status:" << serviceManager.getServiceStatusString();
    qDebug() << "Auto-start enabled:" << (serviceManager.isAutoStartEnabled() ? "Yes" : "No");
    
    // 测试启用自启动
    qDebug() << "\nTesting setAutoStart(true)...";
    if (serviceManager.setAutoStart(true)) {
        qDebug() << "setAutoStart(true) succeeded";
    } else {
        qDebug() << "setAutoStart(true) failed";
    }
    
    // 再次检查状态
    qDebug() << "Auto-start enabled after setting:" << (serviceManager.isAutoStartEnabled() ? "Yes" : "No");
    
    qDebug() << "========================================";
    qDebug() << "Test completed";
    qDebug() << "========================================";
    
    return 0;
}
