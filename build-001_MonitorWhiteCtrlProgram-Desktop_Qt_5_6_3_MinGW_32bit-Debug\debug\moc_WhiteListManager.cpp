/****************************************************************************
** Meta object code from reading C++ file 'WhiteListManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../WhiteListManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'WhiteListManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_WhiteListManager_t {
    QByteArrayData data[8];
    char stringdata0[88];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_WhiteListManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_WhiteListManager_t qt_meta_stringdata_WhiteListManager = {
    {
QT_MOC_LITERAL(0, 0, 16), // "WhiteListManager"
QT_MOC_LITERAL(1, 17, 16), // "whiteListUpdated"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 5), // "count"
QT_MOC_LITERAL(4, 41, 16), // "whiteListCleared"
QT_MOC_LITERAL(5, 58, 13), // "syncCompleted"
QT_MOC_LITERAL(6, 72, 7), // "success"
QT_MOC_LITERAL(7, 80, 7) // "message"

    },
    "WhiteListManager\0whiteListUpdated\0\0"
    "count\0whiteListCleared\0syncCompleted\0"
    "success\0message"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_WhiteListManager[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       3,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   29,    2, 0x06 /* Public */,
       4,    0,   32,    2, 0x06 /* Public */,
       5,    2,   33,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,    6,    7,

       0        // eod
};

void WhiteListManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        WhiteListManager *_t = static_cast<WhiteListManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->whiteListUpdated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 1: _t->whiteListCleared(); break;
        case 2: _t->syncCompleted((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (WhiteListManager::*_t)(int );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&WhiteListManager::whiteListUpdated)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (WhiteListManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&WhiteListManager::whiteListCleared)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (WhiteListManager::*_t)(bool , const QString & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&WhiteListManager::syncCompleted)) {
                *result = 2;
                return;
            }
        }
    }
}

const QMetaObject WhiteListManager::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_WhiteListManager.data,
      qt_meta_data_WhiteListManager,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *WhiteListManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *WhiteListManager::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_WhiteListManager.stringdata0))
        return static_cast<void*>(const_cast< WhiteListManager*>(this));
    return QObject::qt_metacast(_clname);
}

int WhiteListManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void WhiteListManager::whiteListUpdated(int _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void WhiteListManager::whiteListCleared()
{
    QMetaObject::activate(this, &staticMetaObject, 1, Q_NULLPTR);
}

// SIGNAL 2
void WhiteListManager::syncCompleted(bool _t1, const QString & _t2)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_END_MOC_NAMESPACE
