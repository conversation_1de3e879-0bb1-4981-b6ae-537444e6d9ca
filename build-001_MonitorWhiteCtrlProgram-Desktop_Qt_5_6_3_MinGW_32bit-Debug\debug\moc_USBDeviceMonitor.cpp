/****************************************************************************
** Meta object code from reading C++ file 'USBDeviceMonitor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../USBDeviceMonitor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'USBDeviceMonitor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_USBDeviceMonitor_t {
    QByteArrayData data[13];
    char stringdata0[196];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_USBDeviceMonitor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_USBDeviceMonitor_t qt_meta_stringdata_USBDeviceMonitor = {
    {
QT_MOC_LITERAL(0, 0, 16), // "USBDeviceMonitor"
QT_MOC_LITERAL(1, 17, 17), // "usbDeviceInserted"
QT_MOC_LITERAL(2, 35, 0), // ""
QT_MOC_LITERAL(3, 36, 10), // "devicePath"
QT_MOC_LITERAL(4, 47, 16), // "usbDeviceRemoved"
QT_MOC_LITERAL(5, 64, 23), // "whiteListUSBKeyDetected"
QT_MOC_LITERAL(6, 88, 7), // "usbPath"
QT_MOC_LITERAL(7, 96, 18), // "onUSBDeviceChanged"
QT_MOC_LITERAL(8, 115, 25), // "onWhiteListUSBKeyDetected"
QT_MOC_LITERAL(9, 141, 15), // "onScanCompleted"
QT_MOC_LITERAL(10, 157, 10), // "usbDevices"
QT_MOC_LITERAL(11, 168, 13), // "onScanStarted"
QT_MOC_LITERAL(12, 182, 13) // "onScanStopped"

    },
    "USBDeviceMonitor\0usbDeviceInserted\0\0"
    "devicePath\0usbDeviceRemoved\0"
    "whiteListUSBKeyDetected\0usbPath\0"
    "onUSBDeviceChanged\0onWhiteListUSBKeyDetected\0"
    "onScanCompleted\0usbDevices\0onScanStarted\0"
    "onScanStopped"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_USBDeviceMonitor[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   54,    2, 0x06 /* Public */,
       4,    1,   57,    2, 0x06 /* Public */,
       5,    1,   60,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       7,    0,   63,    2, 0x0a /* Public */,
       8,    1,   64,    2, 0x0a /* Public */,
       9,    1,   67,    2, 0x08 /* Private */,
      11,    0,   70,    2, 0x08 /* Private */,
      12,    0,   71,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    6,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QStringList,   10,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void USBDeviceMonitor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        USBDeviceMonitor *_t = static_cast<USBDeviceMonitor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->usbDeviceInserted((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->usbDeviceRemoved((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->whiteListUSBKeyDetected((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->onUSBDeviceChanged(); break;
        case 4: _t->onWhiteListUSBKeyDetected((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->onScanCompleted((*reinterpret_cast< const QStringList(*)>(_a[1]))); break;
        case 6: _t->onScanStarted(); break;
        case 7: _t->onScanStopped(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (USBDeviceMonitor::*_t)(const QString & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&USBDeviceMonitor::usbDeviceInserted)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (USBDeviceMonitor::*_t)(const QString & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&USBDeviceMonitor::usbDeviceRemoved)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (USBDeviceMonitor::*_t)(const QString & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&USBDeviceMonitor::whiteListUSBKeyDetected)) {
                *result = 2;
                return;
            }
        }
    }
}

const QMetaObject USBDeviceMonitor::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_USBDeviceMonitor.data,
      qt_meta_data_USBDeviceMonitor,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *USBDeviceMonitor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *USBDeviceMonitor::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_USBDeviceMonitor.stringdata0))
        return static_cast<void*>(const_cast< USBDeviceMonitor*>(this));
    if (!strcmp(_clname, "QAbstractNativeEventFilter"))
        return static_cast< QAbstractNativeEventFilter*>(const_cast< USBDeviceMonitor*>(this));
    return QObject::qt_metacast(_clname);
}

int USBDeviceMonitor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void USBDeviceMonitor::usbDeviceInserted(const QString & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void USBDeviceMonitor::usbDeviceRemoved(const QString & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void USBDeviceMonitor::whiteListUSBKeyDetected(const QString & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_END_MOC_NAMESPACE
