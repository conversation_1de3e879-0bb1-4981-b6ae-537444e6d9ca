/****************************************************************************
** Meta object code from reading C++ file 'DlgMain_MonitorWhiteCtrlProgram.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../DlgMain_MonitorWhiteCtrlProgram.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'DlgMain_MonitorWhiteCtrlProgram.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_CDlgMain_MonitorWhiteCtrlProgram_t {
    QByteArrayData data[16];
    char stringdata0[243];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CDlgMain_MonitorWhiteCtrlProgram_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CDlgMain_MonitorWhiteCtrlProgram_t qt_meta_stringdata_CDlgMain_MonitorWhiteCtrlProgram = {
    {
QT_MOC_LITERAL(0, 0, 32), // "CDlgMain_MonitorWhiteCtrlProgram"
QT_MOC_LITERAL(1, 33, 16), // "onClearWhiteList"
QT_MOC_LITERAL(2, 50, 0), // ""
QT_MOC_LITERAL(3, 51, 15), // "onSyncWhiteList"
QT_MOC_LITERAL(4, 67, 22), // "onStartServiceFromTray"
QT_MOC_LITERAL(5, 90, 26), // "onUninstallServiceFromTray"
QT_MOC_LITERAL(6, 117, 14), // "showMainWindow"
QT_MOC_LITERAL(7, 132, 10), // "hideToTray"
QT_MOC_LITERAL(8, 143, 17), // "onDisplaysChanged"
QT_MOC_LITERAL(9, 161, 18), // "onWhiteListUpdated"
QT_MOC_LITERAL(10, 180, 5), // "count"
QT_MOC_LITERAL(11, 186, 16), // "onUSBKeyDetected"
QT_MOC_LITERAL(12, 203, 7), // "usbPath"
QT_MOC_LITERAL(13, 211, 15), // "onSyncCompleted"
QT_MOC_LITERAL(14, 227, 7), // "success"
QT_MOC_LITERAL(15, 235, 7) // "message"

    },
    "CDlgMain_MonitorWhiteCtrlProgram\0"
    "onClearWhiteList\0\0onSyncWhiteList\0"
    "onStartServiceFromTray\0"
    "onUninstallServiceFromTray\0showMainWindow\0"
    "hideToTray\0onDisplaysChanged\0"
    "onWhiteListUpdated\0count\0onUSBKeyDetected\0"
    "usbPath\0onSyncCompleted\0success\0message"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CDlgMain_MonitorWhiteCtrlProgram[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   64,    2, 0x0a /* Public */,
       3,    0,   65,    2, 0x0a /* Public */,
       4,    0,   66,    2, 0x0a /* Public */,
       5,    0,   67,    2, 0x0a /* Public */,
       6,    0,   68,    2, 0x0a /* Public */,
       7,    0,   69,    2, 0x0a /* Public */,
       8,    0,   70,    2, 0x08 /* Private */,
       9,    1,   71,    2, 0x08 /* Private */,
      11,    1,   74,    2, 0x08 /* Private */,
      13,    2,   77,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   10,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,   14,   15,

       0        // eod
};

void CDlgMain_MonitorWhiteCtrlProgram::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        CDlgMain_MonitorWhiteCtrlProgram *_t = static_cast<CDlgMain_MonitorWhiteCtrlProgram *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onClearWhiteList(); break;
        case 1: _t->onSyncWhiteList(); break;
        case 2: _t->onStartServiceFromTray(); break;
        case 3: _t->onUninstallServiceFromTray(); break;
        case 4: _t->showMainWindow(); break;
        case 5: _t->hideToTray(); break;
        case 6: _t->onDisplaysChanged(); break;
        case 7: _t->onWhiteListUpdated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 8: _t->onUSBKeyDetected((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 9: _t->onSyncCompleted((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        default: ;
        }
    }
}

const QMetaObject CDlgMain_MonitorWhiteCtrlProgram::staticMetaObject = {
    { &QMainWindow::staticMetaObject, qt_meta_stringdata_CDlgMain_MonitorWhiteCtrlProgram.data,
      qt_meta_data_CDlgMain_MonitorWhiteCtrlProgram,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *CDlgMain_MonitorWhiteCtrlProgram::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CDlgMain_MonitorWhiteCtrlProgram::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_CDlgMain_MonitorWhiteCtrlProgram.stringdata0))
        return static_cast<void*>(const_cast< CDlgMain_MonitorWhiteCtrlProgram*>(this));
    return QMainWindow::qt_metacast(_clname);
}

int CDlgMain_MonitorWhiteCtrlProgram::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 10;
    }
    return _id;
}
QT_END_MOC_NAMESPACE
