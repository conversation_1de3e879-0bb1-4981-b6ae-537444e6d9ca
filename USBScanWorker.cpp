#include "USBScanWorker.h"
#include <QDebug>

#ifdef Q_OS_WIN
#include <winuser.h>
#endif

// 静态常量定义
const QString USBScanWorker::WHITE_LIST_FILE_NAME = "monitor_whitelist.json";

USBScanWorker::USBScanWorker(QObject *parent)
    : QObject(parent)
    , m_scanTimer(new QTimer(this))
    , m_isScanning(false)
    , m_scanInterval(1000)  // 默认1秒
{
    m_scanTimer->setSingleShot(false);
    connect(m_scanTimer, &QTimer::timeout, this, &USBScanWorker::doScan);
    
    qDebug() << "USB scan worker created";
}

USBScanWorker::~USBScanWorker()
{
    stopScanning();
    qDebug() << "USB scan worker destroyed";
}

void USBScanWorker::startScanning()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_isScanning) {
        qDebug() << "USB scanning is already running";
        return;
    }
    
    qDebug() << "Starting USB scanning with" << m_scanInterval << "ms interval";
    
    m_scanTimer->setInterval(m_scanInterval);
    m_scanTimer->start();
    m_isScanning = true;
    
    emit scanStarted();
    
    // 立即执行一次扫描
    doScan();
}

void USBScanWorker::stopScanning()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isScanning) {
        return;
    }
    
    qDebug() << "Stopping USB scanning";
    
    m_scanTimer->stop();
    m_isScanning = false;
    
    emit scanStopped();
}

void USBScanWorker::setScanInterval(int intervalMs)
{
    QMutexLocker locker(&m_mutex);
    
    m_scanInterval = intervalMs;
    
    if (m_isScanning) {
        m_scanTimer->setInterval(m_scanInterval);
    }
    
    qDebug() << "USB scan interval set to" << m_scanInterval << "ms";
}

void USBScanWorker::doScan()
{
    // 获取当前USB设备列表
    QStringList currentDevices = getCurrentUSBDevices();
    
    // 发送扫描结果到主线程
    emit scanCompleted(currentDevices);
}

QStringList USBScanWorker::getCurrentUSBDevices()
{
    QStringList usbDevices;
    
    // 获取所有挂载的存储设备
    QList<QStorageInfo> storageList = QStorageInfo::mountedVolumes();
    
    for (const QStorageInfo &storage : storageList) {
        if (storage.isValid() && storage.isReady()) {
            QString devicePath = storage.device();
            QString rootPath = storage.rootPath();
            
            // 检查是否为可移动设备
            bool isUSB = false;
            
#ifdef Q_OS_WIN
            // Windows: 检查驱动器类型
            UINT driveType = GetDriveType(reinterpret_cast<LPCWSTR>(rootPath.utf16()));
            if (driveType == DRIVE_REMOVABLE) {
                isUSB = true;
            }
#else
            // Linux/Unix: 检查设备路径
            if (devicePath.startsWith("/dev/sd") &&
                (storage.fileSystemType() == "vfat" ||
                 storage.fileSystemType() == "exfat" ||
                 storage.fileSystemType() == "ntfs")) {
                isUSB = true;
            }
#endif
            
            if (isUSB) {
                usbDevices.append(rootPath);
            }
        }
    }
    
    return usbDevices;
}

bool USBScanWorker::checkForWhiteList(const QString &usbPath)
{
    QString whiteListPath = QDir(usbPath).filePath(WHITE_LIST_FILE_NAME);
    return QFile::exists(whiteListPath);
}
