// 简单的编译测试文件
#include <iostream>

#ifdef _WIN32
#include <windows.h>
#include <setupapi.h>
#include <cfgmgr32.h>
#include <initguid.h>

// 定义GUID_DEVCLASS_MONITOR
DEFINE_GUID(GUID_DEVCLASS_MONITOR, 0x4d36e96e, 0xe325, 0x11ce, 0xbf, 0xc1, 0x08, 0x00, 0x2b, 0xe1, 0x03, 0x18);

int main() {
    std::cout << "Testing Windows API compilation..." << std::endl;
    
    // 测试SetupDi函数是否可用
    HDEVINFO hDevInfo = SetupDiGetClassDevs(&GUID_DEVCLASS_MONITOR, nullptr, nullptr, DIGCF_PRESENT);
    if (hDevInfo != INVALID_HANDLE_VALUE) {
        std::cout << "SetupDiGetClassDevs succeeded!" << std::endl;
        SetupDiDestroyDeviceInfoList(hDevInfo);
    } else {
        std::cout << "SetupDiGetClassDevs failed!" << std::endl;
    }
    
    return 0;
}

#else
int main() {
    std::cout << "Non-Windows platform - test skipped" << std::endl;
    return 0;
}
#endif
