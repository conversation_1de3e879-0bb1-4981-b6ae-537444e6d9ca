/********************************************************************************
** Form generated from reading UI file 'DlgMain_MonitorWhiteCtrlProgram.ui'
**
** Created by: Qt User Interface Compiler version 5.6.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_DLGMAIN_MONITORWHITECTRLPROGRAM_H
#define UI_DLGMAIN_MONITORWHITECTRLPROGRAM_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_CDlgMain_MonitorWhiteCtrlProgram
{
public:
    QWidget *centralWidget;

    void setupUi(QMainWindow *CDlgMain_MonitorWhiteCtrlProgram)
    {
        if (CDlgMain_MonitorWhiteCtrlProgram->objectName().isEmpty())
            CDlgMain_MonitorWhiteCtrlProgram->setObjectName(QStringLiteral("CDlgMain_MonitorWhiteCtrlProgram"));
        CDlgMain_MonitorWhiteCtrlProgram->resize(186, 143);
        centralWidget = new QWidget(CDlgMain_MonitorWhiteCtrlProgram);
        centralWidget->setObjectName(QStringLiteral("centralWidget"));
        CDlgMain_MonitorWhiteCtrlProgram->setCentralWidget(centralWidget);

        retranslateUi(CDlgMain_MonitorWhiteCtrlProgram);

        QMetaObject::connectSlotsByName(CDlgMain_MonitorWhiteCtrlProgram);
    } // setupUi

    void retranslateUi(QMainWindow *CDlgMain_MonitorWhiteCtrlProgram)
    {
        CDlgMain_MonitorWhiteCtrlProgram->setWindowTitle(QApplication::translate("CDlgMain_MonitorWhiteCtrlProgram", "CDlgMain_MonitorWhiteCtrlProgram", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class CDlgMain_MonitorWhiteCtrlProgram: public Ui_CDlgMain_MonitorWhiteCtrlProgram {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_DLGMAIN_MONITORWHITECTRLPROGRAM_H
