#ifndef EDIDMANAGER_H
#define EDIDMANAGER_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QMutexLocker>
#include <QWaitCondition>
#include <QList>
#include <QSize>
#include <QByteArray>
#include <QString>
#include <QDebug>
#include <QTextCodec>
#include <QTextStream>

#ifdef Q_OS_WIN
#include <windows.h>
#include <setupapi.h>
#include <cfgmgr32.h>

// 前置声明GUID，避免头文件依赖问题
#ifndef GUID_DEVCLASS_MONITOR
extern "C" const GUID GUID_DEVCLASS_MONITOR;
#endif

#endif

// EDID显示器信息结构体
struct DisplayInfo {
    QString deviceName;          // 设备名称
    QString manufacturer;        // 制造商
    QString productCode;         // 产品代码
    QString serialNumber;        // 序列号
    QString manufactureWeek;     // 制造周
    QString manufactureYear;     // 制造年
    QString edidVersion;         // EDID版本
    QSize nativeResolution;      // 原生分辨率
    QSize physicalSize;          // 物理尺寸(mm)
    QList<QSize> supportedResolutions; // 支持的分辨率
    QByteArray rawEDID;          // 原始EDID数据
    bool isValid;                // 数据是否有效
    bool isPrimary;              // 是否为主显示器
    
    DisplayInfo() : isValid(false), isPrimary(false) {}
};

// 前置声明
class EDIDManager;

// EDID监控线程类
class EDIDMonitorThread : public QThread
{
    Q_OBJECT

public:
    explicit EDIDMonitorThread(EDIDManager *manager, QObject *parent = nullptr);
    ~EDIDMonitorThread();

    // 停止监控线程
    void stopMonitoring();

    // 设置监控间隔（毫秒）
    void setInterval(int intervalMs);

protected:
    void run() override;

signals:
    void displaysChanged();  // 显示器配置改变信号

private:
    EDIDManager *m_manager;
    QMutex m_mutex;
    QWaitCondition m_waitCondition;
    bool m_stopRequested;
    int m_intervalMs;

    QList<DisplayInfo> m_lastDisplaysInfo;

    // 检查显示器配置是否发生变化
    bool hasDisplaysChanged(const QList<DisplayInfo> &newDisplays);
};

// EDID管理器类
class EDIDManager : public QObject
{
    Q_OBJECT
    
public:
    explicit EDIDManager(QObject *parent = nullptr);
    virtual ~EDIDManager();
    
    // 获取所有显示器的EDID信息
    QList<DisplayInfo> getAllDisplaysInfo();
    
    // 刷新显示器信息
    void refreshDisplaysInfo();
    
    // 获取指定设备的EDID信息
    DisplayInfo getDisplayInfo(const QString &deviceName);
    
    // 验证EDID数据
    static bool validateEDID(const QByteArray &edid);
    
    // 解析EDID数据
    static DisplayInfo parseEDID(const QByteArray &edid, const QString &deviceName = QString());

#ifdef Q_OS_WIN
    // Windows平台获取EDID数据（公开给监控线程使用）
    QList<DisplayInfo> getWindowsDisplaysInfo();
#endif

signals:
    void displaysChanged();      // 显示器配置改变信号

private slots:
    void onDisplaysChangedFromThread();  // 从监控线程接收信号的槽函数

private:
    QList<DisplayInfo> m_displaysInfo;
    EDIDMonitorThread *m_monitorThread;
    QMutex m_dataMutex;  // 保护m_displaysInfo的互斥锁

#ifdef Q_OS_WIN
    // Windows平台获取EDID数据
    QByteArray getEDIDFromRegistry(const QString &deviceName);
#endif
    
    // 解析制造商信息
    static QString decodeManufacturer(uchar byte1, uchar byte2);
    
    // 解析支持的分辨率
    static void parseSupportedResolutions(const QByteArray &edid, DisplayInfo &info);
    
    // 解析详细时序描述符
    static void parseDetailedTimingDescriptor(const QByteArray &edid, int offset, DisplayInfo &info);
};

#endif // EDIDMANAGER_H
