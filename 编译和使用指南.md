# 显示器管控程序 - 编译和使用指南

## 🎯 项目概述
基于Qt 5.6.3的Windows显示器管控系统，集成托盘程序、服务功能、USB设备监听和白名单管理等完整功能。

## 📋 系统要求

### 开发环境
- **Qt版本**: Qt 5.6.3 或更高版本
- **编译器**: MinGW 5.3.0 或 MSVC 2015/2017
- **操作系统**: Windows 7/8/10/11
- **架构**: x86 或 x64

### 运行环境
- **操作系统**: Windows 7 SP1 或更高版本
- **权限**: 普通用户权限（服务安装需要管理员权限）
- **依赖库**: Qt 5.6.3 运行时库

## 🔧 编译步骤

### 1. **环境准备**
```bash
# 确保Qt环境变量已设置
echo %QTDIR%
echo %PATH%

# 检查qmake版本
qmake -version
```

### 2. **获取源代码**
```bash
# 进入项目目录
cd E:\Work\Project\001_MonitorWhiteCtrlProgram\001_Code\001_MonitorWhiteCtrlProgram
```

### 3. **生成Makefile**
```bash
# 使用qmake生成Makefile
qmake MonitorWhiteCtrlProgram.pro

# 或指定配置
qmake CONFIG+=release MonitorWhiteCtrlProgram.pro
```

### 4. **编译项目**
```bash
# MinGW编译
mingw32-make

# 或MSVC编译
nmake

# 或使用make（如果环境支持）
make
```

### 5. **编译输出**
编译成功后将生成：
```
001_MonitorWhiteCtrlProgram.exe  # 主程序
```

## 🚀 使用方法

### 1. **普通模式启动**
```bash
# 直接运行，显示主窗口和托盘图标
./001_MonitorWhiteCtrlProgram.exe
```

### 2. **服务模式启动**
```bash
# 后台服务模式运行（需要先安装服务）
./001_MonitorWhiteCtrlProgram.exe --service
```

### 3. **开机启动模式**
```bash
# 最小化到托盘启动
./001_MonitorWhiteCtrlProgram.exe --startup
```

### 4. **管理员模式**
```bash
# 管理员权限运行（用于服务管理）
./001_MonitorWhiteCtrlProgram.exe --admin-mode
```

## 📱 功能使用

### 1. **系统托盘操作**

#### 托盘图标交互
- **单击** - 切换主窗口显示/隐藏
- **双击** - 显示主窗口
- **右键** - 显示操作菜单

#### 托盘菜单功能
```
┌─ 显示主窗口     # 显示程序主界面
├─ 隐藏主窗口     # 隐藏到托盘
├─ ──────────────
├─ 清空白名单     # 清空所有白名单条目
├─ 同步白名单     # 从USB Key同步白名单
├─ ──────────────
├─ 关于          # 显示程序信息
├─ ──────────────
└─ 退出          # 退出程序
```

### 2. **白名单管理**

#### 白名单文件位置
```
本地文件: %APPDATA%\MonitorControl\monitor_whitelist.json
USB文件: [USB根目录]\monitor_whitelist.json
```

#### 白名单文件格式
```json
{
    "version": "1.0",
    "lastModified": "2024-12-07T22:30:00",
    "count": 2,
    "whiteList": [
        {
            "manufacturer": "DEL",
            "productCode": "4156", 
            "serialNumber": "1234567890",
            "deviceName": "Generic PnP Monitor",
            "addTime": "2024-12-07 22:30:00",
            "source": "本地"
        }
    ]
}
```

#### 操作流程
1. **添加显示器到白名单**
   - 连接显示器
   - 程序自动检测EDID信息
   - 手动添加到白名单

2. **USB Key同步**
   - 准备包含白名单文件的USB Key
   - 插入USB设备
   - 程序自动检测并提示同步
   - 或通过托盘菜单手动同步

3. **清空白名单**
   - 右键托盘图标
   - 选择"清空白名单"
   - 确认操作

### 3. **服务管理**

#### 安装服务
```bash
# 以管理员身份运行
./001_MonitorWhiteCtrlProgram.exe --admin-mode

# 在程序中使用ServiceManager安装服务
```

#### 服务配置
```
服务名称: MonitorWhiteCtrlService
显示名称: 显示器管控服务
启动类型: 自动启动
描述: 显示器白名单管控服务，监控显示器EDID信息和USB设备
```

#### 开机自启动
```bash
# 启用开机自启动
ServiceManager::setAutoStart(true)

# 注册表位置
HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run
```

### 4. **USB设备监听**

#### 支持的设备类型
- **Windows**: 可移动驱动器 (DRIVE_REMOVABLE)
- **文件系统**: FAT32, exFAT, NTFS
- **标识文件**: monitor_whitelist.json

#### 监听机制
- **Windows事件**: WM_DEVICECHANGE消息
- **定时扫描**: 2秒间隔轮询
- **自动同步**: 检测到白名单文件时自动同步

## 📊 监控功能

### 1. **EDID监控**
- **监控间隔**: 1秒
- **监控内容**: 显示器EDID数据变化
- **检测事件**: 显示器插拔、配置变更

### 2. **日志记录**
```
日志文件: yyyy-MM-dd_HH-mm-ss-zzz_MonitorCtrl.log
日志内容: 
- 程序启动/退出
- 显示器变化
- 白名单操作
- USB设备事件
- 错误信息
```

## ⚠️ 注意事项

### 1. **权限要求**
- **普通运行**: 无需特殊权限
- **服务安装**: 需要管理员权限
- **注册表操作**: 需要相应权限

### 2. **系统兼容性**
- **Windows 7**: 需要SP1或更高版本
- **Windows 8/10/11**: 完全兼容
- **32位/64位**: 都支持

### 3. **防火墙设置**
程序不需要网络访问，无需配置防火墙。

### 4. **杀毒软件**
某些杀毒软件可能误报，需要添加到白名单。

## 🐛 故障排除

### 1. **编译错误**
```bash
# 检查Qt版本
qmake -version

# 清理编译文件
make clean
qmake
make
```

### 2. **运行时错误**
```bash
# 检查Qt运行时库
# 确保以下DLL在PATH中或程序目录：
Qt5Core.dll
Qt5Gui.dll
Qt5Widgets.dll
```

### 3. **托盘图标不显示**
- 检查系统托盘设置
- 确保系统支持托盘功能
- 重启程序

### 4. **USB设备检测失败**
- 检查USB设备文件系统
- 确保白名单文件存在
- 检查文件权限

## 📞 技术支持

### 日志分析
查看日志文件获取详细错误信息：
```
程序目录\yyyy-MM-dd_HH-mm-ss-zzz_MonitorCtrl.log
```

### 调试模式
```bash
# 启用详细日志输出
./001_MonitorWhiteCtrlProgram.exe --debug
```

## ✅ 部署清单

### 发布文件
```
001_MonitorWhiteCtrlProgram.exe  # 主程序
Qt5Core.dll                     # Qt核心库
Qt5Gui.dll                      # Qt GUI库  
Qt5Widgets.dll                  # Qt控件库
platforms\qwindows.dll          # Windows平台插件
```

### 可选文件
```
monitor_whitelist.json           # 示例白名单文件
README.md                       # 使用说明
```

**编译和部署完成后，即可在Windows环境中使用完整的显示器管控功能！** 🎉
