#include "EDIDManager.h"
#include <string>

#ifdef Q_OS_WIN
#include <initguid.h>  // 必须在其他包含GUID的头文件之前

// 定义GUID_DEVCLASS_MONITOR，如果没有在devguid.h中定义
#ifndef GUID_DEVCLASS_MONITOR
DEFINE_GUID(GUID_DEVCLASS_MONITOR, 0x4d36e96e, 0xe325, 0x11ce, 0xbf, 0xc1, 0x08, 0x00, 0x2b, 0xe1, 0x03, 0x18);
#endif

#endif

// ===============================
// EDIDMonitorThread 类实现
// ===============================

EDIDMonitorThread::EDIDMonitorThread(EDIDManager *manager, QObject *parent)
    : QThread(parent)
    , m_manager(manager)
    , m_stopRequested(false)
    , m_intervalMs(1000)  // 默认1秒间隔
{
}

EDIDMonitorThread::~EDIDMonitorThread()
{
    stopMonitoring();
}

void EDIDMonitorThread::stopMonitoring()
{
    QMutexLocker locker(&m_mutex);
    m_stopRequested = true;
    m_waitCondition.wakeAll();
    locker.unlock();

    // 等待线程结束
    if (isRunning()) {
        wait(3000);  // 最多等待3秒
        if (isRunning()) {
            terminate();  // 强制终止
            wait(1000);
        }
    }
}

void EDIDMonitorThread::setInterval(int intervalMs)
{
    QMutexLocker locker(&m_mutex);
    m_intervalMs = intervalMs;
}

void EDIDMonitorThread::run()
{
    qDebug() << "EDID monitor thread started, interval:" << m_intervalMs << "ms";

    // 初始化上次的显示器信息
    if (m_manager) {
#ifdef Q_OS_WIN
        m_lastDisplaysInfo = m_manager->getWindowsDisplaysInfo();
#endif
    }

    while (true) {
        QMutexLocker locker(&m_mutex);

        // 检查是否需要停止
        if (m_stopRequested) {
            qDebug() << "EDID monitor thread received stop request";
            break;
        }

        // 等待指定间隔时间
        m_waitCondition.wait(&m_mutex, m_intervalMs);

        // 再次检查是否需要停止
        if (m_stopRequested) {
            break;
        }

        locker.unlock();

        // 获取当前显示器信息
        if (m_manager) {
#ifdef Q_OS_WIN
            QList<DisplayInfo> currentDisplays = m_manager->getWindowsDisplaysInfo();

            // 检查是否有变化
            if (hasDisplaysChanged(currentDisplays)) {
                qDebug() << "Monitor configuration change detected";
                m_lastDisplaysInfo = currentDisplays;
                emit displaysChanged();
            }
#endif
        }
    }

    qDebug() << "EDID monitor thread ended";
}

bool EDIDMonitorThread::hasDisplaysChanged(const QList<DisplayInfo> &newDisplays)
{
    // 检查显示器数量是否变化
    if (newDisplays.size() != m_lastDisplaysInfo.size()) {
        return true;
    }

    // 检查每个显示器的EDID数据是否变化
    for (int i = 0; i < newDisplays.size(); ++i) {
        bool found = false;
        for (int j = 0; j < m_lastDisplaysInfo.size(); ++j) {
            if (newDisplays[i].deviceName == m_lastDisplaysInfo[j].deviceName &&
                newDisplays[i].rawEDID == m_lastDisplaysInfo[j].rawEDID) {
                found = true;
                break;
            }
        }
        if (!found) {
            return true;
        }
    }

    return false;
}

// ===============================
// EDIDManager 类实现
// ===============================

EDIDManager::EDIDManager(QObject *parent)
    : QObject(parent)
    , m_monitorThread(nullptr)
{
    // 初始加载显示器信息
    refreshDisplaysInfo();

    // 创建并启动监控线程
    m_monitorThread = new EDIDMonitorThread(this, this);
    m_monitorThread->setInterval(1000);  // 设置1秒间隔

    // 连接线程信号到槽函数
    connect(m_monitorThread, &EDIDMonitorThread::displaysChanged,
            this, &EDIDManager::onDisplaysChangedFromThread);

    // 启动监控线程
    m_monitorThread->start();

    qDebug() << "EDIDManager initialized, monitor thread started";
}

EDIDManager::~EDIDManager()
{
    if (m_monitorThread) {
        m_monitorThread->stopMonitoring();
        delete m_monitorThread;
        m_monitorThread = nullptr;
    }
}

QList<DisplayInfo> EDIDManager::getAllDisplaysInfo()
{
    QMutexLocker locker(&m_dataMutex);
    return m_displaysInfo;
}

void EDIDManager::refreshDisplaysInfo()
{
    QList<DisplayInfo> newDisplaysInfo;

#ifdef Q_OS_WIN
    newDisplaysInfo = getWindowsDisplaysInfo();
#endif

    QMutexLocker locker(&m_dataMutex);

    // 检查是否有变化
    bool hasChanged = (newDisplaysInfo.size() != m_displaysInfo.size());
    if (!hasChanged) {
        for (int i = 0; i < newDisplaysInfo.size(); ++i) {
            if (newDisplaysInfo[i].deviceName != m_displaysInfo[i].deviceName ||
                newDisplaysInfo[i].rawEDID != m_displaysInfo[i].rawEDID) {
                hasChanged = true;
                break;
            }
        }
    }

    if (hasChanged) {
        m_displaysInfo = newDisplaysInfo;
        locker.unlock();  // 在发射信号前解锁
        emit displaysChanged();
    }
}

DisplayInfo EDIDManager::getDisplayInfo(const QString &deviceName)
{
    QMutexLocker locker(&m_dataMutex);
    for (const DisplayInfo &info : m_displaysInfo) {
        if (info.deviceName == deviceName) {
            return info;
        }
    }
    return DisplayInfo(); // 返回无效的DisplayInfo
}

bool EDIDManager::validateEDID(const QByteArray &edid)
{
    if (edid.size() < 128) {
        return false;
    }
    
    // 检查EDID头部标识 (00 FF FF FF FF FF FF 00)
    const char expectedHeader[] = {0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00};
    if (edid.left(8) != QByteArray(expectedHeader, 8)) {
        return false;
    }
    
    // 验证校验和
    int checksum = 0;
    for (int i = 0; i < 128; ++i) {
        checksum += static_cast<unsigned char>(edid[i]);
    }
    
    return (checksum & 0xFF) == 0;
}

void EDIDManager::onDisplaysChangedFromThread()
{
    // 从监控线程接收到显示器配置变化信号
    qDebug() << "Received monitor configuration change signal from monitor thread";

    // 刷新主线程中的显示器信息
    refreshDisplaysInfo();
}

#ifdef Q_OS_WIN
QList<DisplayInfo> EDIDManager::getWindowsDisplaysInfo()
{
    QList<DisplayInfo> displaysInfo;
    
    HDEVINFO hDevInfo = SetupDiGetClassDevs(&GUID_DEVCLASS_MONITOR, nullptr, nullptr, DIGCF_PRESENT);
    if (hDevInfo == INVALID_HANDLE_VALUE) {
        qWarning() << "Failed to get monitor device info";
        return displaysInfo;
    }
    
    SP_DEVINFO_DATA devInfoData;
    devInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
    
    for (DWORD i = 0; SetupDiEnumDeviceInfo(hDevInfo, i, &devInfoData); ++i) {
        // 获取设备名称
        WCHAR deviceName[256];
        if (!SetupDiGetDeviceRegistryProperty(hDevInfo, &devInfoData, SPDRP_FRIENDLYNAME,
                                            nullptr, reinterpret_cast<PBYTE>(deviceName),
                                            sizeof(deviceName), nullptr)) {
            continue;
        }
        
        QString deviceNameStr = QString::fromWCharArray(deviceName);
        
        // 获取EDID数据
        HKEY hKey = SetupDiOpenDevRegKey(hDevInfo, &devInfoData, DICS_FLAG_GLOBAL, 0, DIREG_DEV, KEY_READ);
        if (hKey != INVALID_HANDLE_VALUE) {
            BYTE data[512];
            DWORD size = sizeof(data);
            DWORD type = 0;
            
            if (RegQueryValueEx(hKey, TEXT("EDID"), nullptr, &type, data, &size) == ERROR_SUCCESS) {
                if (size >= 128) {
                    QByteArray edidData(reinterpret_cast<char*>(data), static_cast<int>(size));
                    
                    if (validateEDID(edidData)) {
                        DisplayInfo info = parseEDID(edidData, deviceNameStr);
                        if (info.isValid) {
                            displaysInfo.append(info);
                        }
                    }
                }
            }
            RegCloseKey(hKey);
        }
    }
    
    SetupDiDestroyDeviceInfoList(hDevInfo);
    return displaysInfo;
}

QByteArray EDIDManager::getEDIDFromRegistry(const QString &deviceName)
{
    // 这个方法可以用于获取特定设备的EDID数据
    // 当前在getWindowsDisplaysInfo中已经实现了类似功能
    Q_UNUSED(deviceName)
    return QByteArray();
}
#endif

// EDID解析功能实现
DisplayInfo EDIDManager::parseEDID(const QByteArray &edid, const QString &deviceName)
{
    DisplayInfo info;
    info.deviceName = deviceName;
    info.rawEDID = edid;

    if (edid.size() < 128) {
        qWarning() << "Invalid EDID data (too small)";
        return info;
    }

    // 解析制造商信息 (字节8-9)
    info.manufacturer = decodeManufacturer(static_cast<uchar>(edid[8]), static_cast<uchar>(edid[9]));

    // 产品代码 (字节10-11, 小端序)
    quint16 productCode = (static_cast<uchar>(edid[11]) << 8) | static_cast<uchar>(edid[10]);
    info.productCode = QString("%1").arg(productCode, 4, 16, QChar('0')).toUpper();

    // 序列号 (字节12-15, 小端序)
    quint32 serialNum = (static_cast<uchar>(edid[15]) << 24) |
                       (static_cast<uchar>(edid[14]) << 16) |
                       (static_cast<uchar>(edid[13]) << 8) |
                       static_cast<uchar>(edid[12]);
    info.serialNumber = QString::number(serialNum);

    // 制造日期 (字节16-17)
    info.manufactureWeek = QString::number(static_cast<uchar>(edid[16]));
    info.manufactureYear = QString::number(static_cast<uchar>(edid[17]) + 1990);

    // EDID版本 (字节18-19)
    info.edidVersion = QString("%1.%2").arg(static_cast<uchar>(edid[18])).arg(static_cast<uchar>(edid[19]));

    // 物理尺寸 (字节21-22, 单位cm)
    int width = static_cast<uchar>(edid[21]);
    int height = static_cast<uchar>(edid[22]);
    info.physicalSize = QSize(width * 10, height * 10); // 转换为mm

    // 解析详细时序描述符获取原生分辨率
    parseDetailedTimingDescriptor(edid, 54, info);

    // 解析支持的分辨率
    parseSupportedResolutions(edid, info);

    info.isValid = true;
    return info;
}

QString EDIDManager::decodeManufacturer(uchar byte1, uchar byte2)
{
    QString result;

    // 制造商ID是15位，分成3个5位字段，每个字段+64得到ASCII码
    int id1 = ((byte1 >> 2) & 0x1F);
    int id2 = (((byte1 & 0x03) << 3) | ((byte2 >> 5) & 0x07));
    int id3 = (byte2 & 0x1F);

    if (id1 > 0 && id1 <= 26) result += QChar('A' + id1 - 1);
    if (id2 > 0 && id2 <= 26) result += QChar('A' + id2 - 1);
    if (id3 > 0 && id3 <= 26) result += QChar('A' + id3 - 1);

    return result;
}

void EDIDManager::parseSupportedResolutions(const QByteArray &edid, DisplayInfo &info)
{
    // 解析标准时序标识 (字节35-37)
    for (int i = 35; i <= 37; ++i) {
        uchar byte = static_cast<uchar>(edid[i]);
        for (int bit = 7; bit >= 0; --bit) {
            if (byte & (1 << bit)) {
                // 根据EDID标准添加对应的分辨率
                // 这里简化处理，实际应该根据标准映射表
                int resIndex = (i - 35) * 8 + (7 - bit);
                switch (resIndex) {
                    case 0: info.supportedResolutions.append(QSize(720, 400)); break;
                    case 1: info.supportedResolutions.append(QSize(720, 400)); break;
                    case 2: info.supportedResolutions.append(QSize(640, 480)); break;
                    case 3: info.supportedResolutions.append(QSize(640, 480)); break;
                    case 4: info.supportedResolutions.append(QSize(640, 480)); break;
                    case 5: info.supportedResolutions.append(QSize(640, 480)); break;
                    case 6: info.supportedResolutions.append(QSize(800, 600)); break;
                    case 7: info.supportedResolutions.append(QSize(800, 600)); break;
                    case 8: info.supportedResolutions.append(QSize(800, 600)); break;
                    case 9: info.supportedResolutions.append(QSize(1024, 768)); break;
                    case 10: info.supportedResolutions.append(QSize(1024, 768)); break;
                    case 11: info.supportedResolutions.append(QSize(1024, 768)); break;
                    case 12: info.supportedResolutions.append(QSize(1280, 1024)); break;
                    case 13: info.supportedResolutions.append(QSize(1152, 870)); break;
                    // 可以继续添加更多标准分辨率
                }
            }
        }
    }

    // 解析详细时序描述符中的分辨率 (字节54-125)
    for (int i = 54; i <= 108; i += 18) {
        parseDetailedTimingDescriptor(edid, i, info);
    }
}

void EDIDManager::parseDetailedTimingDescriptor(const QByteArray &edid, int offset, DisplayInfo &info)
{
    if (offset + 17 >= edid.size()) return;

    // 检查是否为详细时序描述符 (前两个字节不为0)
    if (edid[offset] == 0x00 && edid[offset + 1] == 0x00) {
        return; // 这是其他类型的描述符
    }

    // 解析水平和垂直分辨率
    int hActive = static_cast<uchar>(edid[offset + 2]) |
                 ((static_cast<uchar>(edid[offset + 4]) & 0xF0) << 4);
    int vActive = static_cast<uchar>(edid[offset + 5]) |
                 ((static_cast<uchar>(edid[offset + 7]) & 0xF0) << 4);

    if (hActive > 0 && vActive > 0) {
        QSize resolution(hActive, vActive);

        // 如果这是第一个有效的详细时序描述符，设为原生分辨率
        if (info.nativeResolution.isEmpty()) {
            info.nativeResolution = resolution;
        }

        // 添加到支持的分辨率列表（避免重复）
        if (!info.supportedResolutions.contains(resolution)) {
            info.supportedResolutions.append(resolution);
        }
    }
}

// 包含MOC生成的代码
//#include "EDIDManager.moc"
