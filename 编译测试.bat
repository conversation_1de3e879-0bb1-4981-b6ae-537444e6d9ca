@echo off
echo ========================================
echo 显示器管控程序编译测试
echo ========================================

echo.
echo 1. 检查Qt环境...
qmake -version
if %errorlevel% neq 0 (
    echo 错误: qmake未找到，请检查Qt环境配置
    pause
    exit /b 1
)

echo.
echo 2. 清理旧的编译文件...
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug
if exist Makefile.Release del Makefile.Release
if exist *.o del *.o
if exist debug rmdir /s /q debug
if exist release rmdir /s /q release

echo.
echo 3. 生成Makefile...
qmake MonitorWhiteCtrlProgram.pro
if %errorlevel% neq 0 (
    echo 错误: qmake执行失败
    pause
    exit /b 1
)

echo.
echo 4. 开始编译...
mingw32-make
if %errorlevel% neq 0 (
    echo 错误: 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo 编译成功！
echo ========================================
echo.
echo 生成的可执行文件:
if exist 001_MonitorWhiteCtrlProgram.exe (
    echo   001_MonitorWhiteCtrlProgram.exe
    echo.
    echo 文件大小:
    dir 001_MonitorWhiteCtrlProgram.exe | find "001_MonitorWhiteCtrlProgram.exe"
) else (
    echo   未找到可执行文件
)

echo.
echo 按任意键退出...
pause > nul
