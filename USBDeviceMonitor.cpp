#include "USBDeviceMonitor.h"
#include "USBScanWorker.h"
#include "WhiteListManager.h"
#include <QApplication>
#include <QWidget>
#include <QDir>
#include <QFile>
#include <QDebug>

#ifdef Q_OS_WIN
#include <dbt.h>
#include <winuser.h>
#endif

// 静态常量定义
const int USBDeviceMonitor::SCAN_INTERVAL_MS = 1000;  // 1秒扫描间隔
const QString USBDeviceMonitor::WHITE_LIST_FILE_NAME = "monitor_whitelist.json";

USBDeviceMonitor::USBDeviceMonitor(WhiteListManager *whiteListManager, QObject *parent)
    : QObject(parent)
    , m_whiteListManager(whiteListManager)
    , m_isMonitoring(false)
    , m_scanThread(nullptr)
    , m_scanWorker(nullptr)
#ifdef Q_OS_WIN
    , m_deviceNotify(nullptr)
#endif
{
    // 创建扫描工作线程
    m_scanThread = new QThread(this);
    m_scanWorker = new USBScanWorker();

    // 将工作对象移动到工作线程
    m_scanWorker->moveToThread(m_scanThread);

    // 设置扫描间隔
    m_scanWorker->setScanInterval(SCAN_INTERVAL_MS);

    // 连接工作线程信号槽
    connect(m_scanThread, &QThread::started, m_scanWorker, &USBScanWorker::startScanning);
    connect(m_scanThread, &QThread::finished, m_scanWorker, &USBScanWorker::stopScanning);
    connect(m_scanWorker, &USBScanWorker::scanCompleted, this, &USBDeviceMonitor::onScanCompleted);
    connect(m_scanWorker, &USBScanWorker::scanStarted, this, &USBDeviceMonitor::onScanStarted);
    connect(m_scanWorker, &USBScanWorker::scanStopped, this, &USBDeviceMonitor::onScanStopped);

    // 连接其他信号槽
    connect(this, &USBDeviceMonitor::whiteListUSBKeyDetected,
            this, &USBDeviceMonitor::onWhiteListUSBKeyDetected);

    qDebug() << "USB device monitor initialized with multithreading support";
}

USBDeviceMonitor::~USBDeviceMonitor()
{
    stopMonitoring();

    // 清理工作线程
    if (m_scanThread) {
        if (m_scanThread->isRunning()) {
            m_scanThread->quit();
            if (!m_scanThread->wait(3000)) {
                qWarning() << "USB scan thread failed to quit gracefully, terminating...";
                m_scanThread->terminate();
                m_scanThread->wait(1000);
            }
        }
    }

    // 清理工作对象
    if (m_scanWorker) {
        m_scanWorker->deleteLater();
        m_scanWorker = nullptr;
    }

    qDebug() << "USB device monitor destroyed";
}

void USBDeviceMonitor::startMonitoring()
{
    if (m_isMonitoring) {
        qDebug() << "USB device monitoring is already running";
        return;
    }

    qDebug() << "Starting USB device monitoring with multithreading (1-second interval)";

    // 获取初始USB设备列表
    QMutexLocker locker(&m_deviceListMutex);
    m_lastUSBDevices = getCurrentUSBDevices();
    locker.unlock();

    qDebug() << "Initial USB devices:" << m_lastUSBDevices;

    // 初始化监控
    initializeMonitoring();

    // 启动工作线程
    if (m_scanThread && !m_scanThread->isRunning()) {
        m_scanThread->start();
        qDebug() << "USB scan thread started";
    }

    m_isMonitoring = true;
}

void USBDeviceMonitor::stopMonitoring()
{
    if (!m_isMonitoring) {
        return;
    }

    qDebug() << "Stopping USB device monitoring";

    // 停止工作线程
    if (m_scanThread && m_scanThread->isRunning()) {
        m_scanThread->quit();
        if (!m_scanThread->wait(3000)) {
            qWarning() << "USB scan thread failed to quit gracefully";
            m_scanThread->terminate();
            m_scanThread->wait(1000);
        }
        qDebug() << "USB scan thread stopped";
    }

    cleanupMonitoring();

    m_isMonitoring = false;
}

QStringList USBDeviceMonitor::getCurrentUSBDevices()
{
    QStringList usbDevices;
    
    // 获取所有挂载的存储设备
    QList<QStorageInfo> storageList = QStorageInfo::mountedVolumes();
    
    for (const QStorageInfo &storage : storageList) {
        if (storage.isValid() && storage.isReady()) {
            QString devicePath = storage.device();
            QString rootPath = storage.rootPath();
            
            // 检查是否为可移动设备
            bool isUSB = false;
            
#ifdef Q_OS_WIN
            // Windows: 检查驱动器类型
            UINT driveType = GetDriveType(reinterpret_cast<LPCWSTR>(rootPath.utf16()));
            if (driveType == DRIVE_REMOVABLE) {
                isUSB = true;
            }
#else
            // Linux/Unix: 检查设备路径
            if (devicePath.startsWith("/dev/sd") && 
                (storage.fileSystemType() == "vfat" || 
                 storage.fileSystemType() == "exfat" ||
                 storage.fileSystemType() == "ntfs")) {
                isUSB = true;
            }
#endif
            
            if (isUSB) {
                usbDevices.append(rootPath);
            }
        }
    }
    
    return usbDevices;
}

void USBDeviceMonitor::scanUSBDevices()
{
    // 这个方法现在由工作线程处理，主线程通过onScanCompleted接收结果
    qDebug() << "Manual scan requested - triggering worker scan";
    if (m_scanWorker) {
        QMetaObject::invokeMethod(m_scanWorker, "doScan", Qt::QueuedConnection);
    }
}

bool USBDeviceMonitor::checkForWhiteList(const QString &usbPath)
{
    QString whiteListPath = QDir(usbPath).filePath(WHITE_LIST_FILE_NAME);
    return QFile::exists(whiteListPath);
}

void USBDeviceMonitor::compareUSBDevices(const QStringList &newDevices)
{
    // 这个方法在scanUSBDevices中已经实现
    Q_UNUSED(newDevices)
}

void USBDeviceMonitor::onUSBDeviceChanged()
{
    qDebug() << "USB设备状态发生变化，重新扫描";
    scanUSBDevices();
}

void USBDeviceMonitor::onWhiteListUSBKeyDetected(const QString &usbPath)
{
    if (!m_whiteListManager) {
        qWarning() << "白名单管理器未初始化";
        return;
    }
    
    qDebug() << "处理白名单USB Key:" << usbPath;
    
    // 自动同步白名单
    bool success = m_whiteListManager->syncFromUSBKey(usbPath);
    
    if (success) {
        qDebug() << "自动同步白名单成功";
    } else {
        qWarning() << "自动同步白名单失败";
    }
}

void USBDeviceMonitor::onScanCompleted(const QStringList &usbDevices)
{
    QMutexLocker locker(&m_deviceListMutex);

    // 检查新插入的设备
    for (const QString &device : usbDevices) {
        if (!m_lastUSBDevices.contains(device)) {
            qDebug() << "Detected new USB device:" << device;
            emit usbDeviceInserted(device);

            // 检查是否包含白名单
            if (checkForWhiteList(device)) {
                qDebug() << "Found USB key with whitelist:" << device;
                emit whiteListUSBKeyDetected(device);
            }
        }
    }

    // 检查移除的设备
    for (const QString &device : m_lastUSBDevices) {
        if (!usbDevices.contains(device)) {
            qDebug() << "Detected USB device removal:" << device;
            emit usbDeviceRemoved(device);
        }
    }

    m_lastUSBDevices = usbDevices;
}

void USBDeviceMonitor::onScanStarted()
{
    qDebug() << "USB scan worker started";
}

void USBDeviceMonitor::onScanStopped()
{
    qDebug() << "USB scan worker stopped";
}

void USBDeviceMonitor::initializeMonitoring()
{
#ifdef Q_OS_WIN
    registerDeviceNotification();
    
    // 安装原生事件过滤器
    QApplication::instance()->installNativeEventFilter(this);
#endif
}

void USBDeviceMonitor::cleanupMonitoring()
{
#ifdef Q_OS_WIN
    unregisterDeviceNotification();
    
    // 移除原生事件过滤器
    QApplication::instance()->removeNativeEventFilter(this);
#endif
}

#ifdef Q_OS_WIN
void USBDeviceMonitor::registerDeviceNotification()
{
    // 注册设备变化通知
    DEV_BROADCAST_DEVICEINTERFACE dbdi;
    ZeroMemory(&dbdi, sizeof(dbdi));
    dbdi.dbcc_size = sizeof(dbdi);
    dbdi.dbcc_devicetype = DBT_DEVTYP_DEVICEINTERFACE;
    
    // 获取主窗口句柄
    HWND hwnd = nullptr;
    if (QWidget *widget = QApplication::activeWindow()) {
        hwnd = reinterpret_cast<HWND>(widget->winId());
    }
    
    if (hwnd) {
        m_deviceNotify = RegisterDeviceNotification(hwnd, &dbdi, DEVICE_NOTIFY_WINDOW_HANDLE);
        if (m_deviceNotify) {
            qDebug() << "Windows设备通知注册成功";
        } else {
            qWarning() << "Windows设备通知注册失败";
        }
    }
}

void USBDeviceMonitor::unregisterDeviceNotification()
{
    if (m_deviceNotify) {
        UnregisterDeviceNotification(m_deviceNotify);
        m_deviceNotify = nullptr;
        qDebug() << "Windows设备通知已注销";
    }
}

bool USBDeviceMonitor::nativeEventFilter(const QByteArray &eventType, void *message, long *result)
{
    Q_UNUSED(result)
    
    if (eventType == "windows_generic_MSG") {
        MSG *msg = static_cast<MSG*>(message);
        
        if (msg->message == WM_DEVICECHANGE) {
            WPARAM wParam = msg->wParam;
            
            switch (wParam) {
            case DBT_DEVICEARRIVAL:
                qDebug() << "Windows检测到设备插入";
                // 延迟扫描，等待设备完全就绪
                QTimer::singleShot(1000, this, &USBDeviceMonitor::onUSBDeviceChanged);
                break;
                
            case DBT_DEVICEREMOVECOMPLETE:
                qDebug() << "Windows检测到设备移除";
                QTimer::singleShot(500, this, &USBDeviceMonitor::onUSBDeviceChanged);
                break;
                
            default:
                break;
            }
        }
    }
    
    return false;  // 继续传递事件
}
#endif
