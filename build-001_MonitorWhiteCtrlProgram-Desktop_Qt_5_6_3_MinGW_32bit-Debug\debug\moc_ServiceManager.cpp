/****************************************************************************
** Meta object code from reading C++ file 'ServiceManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../ServiceManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ServiceManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_ServiceManager_t {
    QByteArrayData data[9];
    char stringdata0[124];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ServiceManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ServiceManager_t qt_meta_stringdata_ServiceManager = {
    {
QT_MOC_LITERAL(0, 0, 14), // "ServiceManager"
QT_MOC_LITERAL(1, 15, 20), // "serviceStatusChanged"
QT_MOC_LITERAL(2, 36, 0), // ""
QT_MOC_LITERAL(3, 37, 13), // "ServiceStatus"
QT_MOC_LITERAL(4, 51, 6), // "status"
QT_MOC_LITERAL(5, 58, 16), // "serviceInstalled"
QT_MOC_LITERAL(6, 75, 18), // "serviceUninstalled"
QT_MOC_LITERAL(7, 94, 14), // "serviceStarted"
QT_MOC_LITERAL(8, 109, 14) // "serviceStopped"

    },
    "ServiceManager\0serviceStatusChanged\0"
    "\0ServiceStatus\0status\0serviceInstalled\0"
    "serviceUninstalled\0serviceStarted\0"
    "serviceStopped"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ServiceManager[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   39,    2, 0x06 /* Public */,
       5,    0,   42,    2, 0x06 /* Public */,
       6,    0,   43,    2, 0x06 /* Public */,
       7,    0,   44,    2, 0x06 /* Public */,
       8,    0,   45,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void ServiceManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        ServiceManager *_t = static_cast<ServiceManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->serviceStatusChanged((*reinterpret_cast< ServiceStatus(*)>(_a[1]))); break;
        case 1: _t->serviceInstalled(); break;
        case 2: _t->serviceUninstalled(); break;
        case 3: _t->serviceStarted(); break;
        case 4: _t->serviceStopped(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (ServiceManager::*_t)(ServiceStatus );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&ServiceManager::serviceStatusChanged)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (ServiceManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&ServiceManager::serviceInstalled)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (ServiceManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&ServiceManager::serviceUninstalled)) {
                *result = 2;
                return;
            }
        }
        {
            typedef void (ServiceManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&ServiceManager::serviceStarted)) {
                *result = 3;
                return;
            }
        }
        {
            typedef void (ServiceManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&ServiceManager::serviceStopped)) {
                *result = 4;
                return;
            }
        }
    }
}

const QMetaObject ServiceManager::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_ServiceManager.data,
      qt_meta_data_ServiceManager,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *ServiceManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ServiceManager::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_ServiceManager.stringdata0))
        return static_cast<void*>(const_cast< ServiceManager*>(this));
    return QObject::qt_metacast(_clname);
}

int ServiceManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void ServiceManager::serviceStatusChanged(ServiceStatus _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ServiceManager::serviceInstalled()
{
    QMetaObject::activate(this, &staticMetaObject, 1, Q_NULLPTR);
}

// SIGNAL 2
void ServiceManager::serviceUninstalled()
{
    QMetaObject::activate(this, &staticMetaObject, 2, Q_NULLPTR);
}

// SIGNAL 3
void ServiceManager::serviceStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 3, Q_NULLPTR);
}

// SIGNAL 4
void ServiceManager::serviceStopped()
{
    QMetaObject::activate(this, &staticMetaObject, 4, Q_NULLPTR);
}
QT_END_MOC_NAMESPACE
