#############################################################################
# Makefile for building: 001_MonitorWhiteCtrlProgram
# Generated by qmake (3.0) (Qt 5.6.3)
# Project:  ..\MonitorWhiteCtrlProgram.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -DUNICODE -D_UNICODE -DQT_QML_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -pipe -fno-keep-inline-dllexport -g -Wall -Wextra $(DEFINES)
CXXFLAGS      = -pipe -fno-keep-inline-dllexport -g -std=gnu++0x -frtti -Wall -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I..\..\001_MonitorWhiteCtrlProgram -I. -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\include -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\include\QtWidgets -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\include\QtGui -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\include\QtANGLE -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\include\QtCore -Idebug -I. -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        -lmingw32 -LD:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\libqtmaind.a -lshell32 -LC:\utils\my_sql\my_sql\lib -LC:\utils\postgresql\pgsql\lib -lsetupapi -ladvapi32 -luser32 D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\libQt5Widgetsd.a D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\libQt5Guid.a D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\libQt5Cored.a 
QMAKE         = D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\qmake.exe
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
COPY          = copy /y
SED           = $(QMAKE) -install sed
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
DEL_FILE      = del
DEL_DIR       = rmdir
MOVE          = move
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
INSTALL_FILE    = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR     = xcopy /s /q /y /i

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = ..\main.cpp \
		..\DlgMain_MonitorWhiteCtrlProgram.cpp \
		..\EDIDManager.cpp \
		..\TrayManager.cpp \
		..\WhiteListManager.cpp \
		..\USBDeviceMonitor.cpp \
		..\USBScanWorker.cpp \
		..\ServiceManager.cpp debug\moc_DlgMain_MonitorWhiteCtrlProgram.cpp \
		debug\moc_EDIDManager.cpp \
		debug\moc_TrayManager.cpp \
		debug\moc_WhiteListManager.cpp \
		debug\moc_USBDeviceMonitor.cpp \
		debug\moc_USBScanWorker.cpp \
		debug\moc_ServiceManager.cpp
OBJECTS       = debug/main.o \
		debug/DlgMain_MonitorWhiteCtrlProgram.o \
		debug/EDIDManager.o \
		debug/TrayManager.o \
		debug/WhiteListManager.o \
		debug/USBDeviceMonitor.o \
		debug/USBScanWorker.o \
		debug/ServiceManager.o \
		debug/moc_DlgMain_MonitorWhiteCtrlProgram.o \
		debug/moc_EDIDManager.o \
		debug/moc_TrayManager.o \
		debug/moc_WhiteListManager.o \
		debug/moc_USBDeviceMonitor.o \
		debug/moc_USBScanWorker.o \
		debug/moc_ServiceManager.o

DIST          =  ..\DlgMain_MonitorWhiteCtrlProgram.h \
		..\EDIDManager.h \
		..\TrayManager.h \
		..\WhiteListManager.h \
		..\USBDeviceMonitor.h \
		..\USBScanWorker.h \
		..\ServiceManager.h ..\main.cpp \
		..\DlgMain_MonitorWhiteCtrlProgram.cpp \
		..\EDIDManager.cpp \
		..\TrayManager.cpp \
		..\WhiteListManager.cpp \
		..\USBDeviceMonitor.cpp \
		..\USBScanWorker.cpp \
		..\ServiceManager.cpp
QMAKE_TARGET  = 001_MonitorWhiteCtrlProgram
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = 001_MonitorWhiteCtrlProgram.exe
DESTDIR_TARGET = debug\001_MonitorWhiteCtrlProgram.exe

####### Build rules

first: all
all: Makefile.Debug  $(DESTDIR_TARGET)

$(DESTDIR_TARGET): ui_DlgMain_MonitorWhiteCtrlProgram.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) object_script.001_MonitorWhiteCtrlProgram.Debug  $(LIBS)

qmake: FORCE
	@$(QMAKE) -spec win32-g++ "CONFIG+=debug" "CONFIG+=qml_debug" -o Makefile.Debug ..\MonitorWhiteCtrlProgram.pro

qmake_all: FORCE

dist:
	$(ZIP) 001_MonitorWhiteCtrlProgram.zip $(SOURCES) $(DIST) ..\MonitorWhiteCtrlProgram.pro D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\spec_pre.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\qdevice.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\device_config.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\common\angle.conf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\qconfig.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dcore.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dcore_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dinput.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dinput_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dlogic.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dlogic_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquick.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquick_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquickinput.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquickinput_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquickrender.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquickrender_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3drender.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3drender_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axbase.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axbase_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axcontainer.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axcontainer_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axserver.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axserver_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_bluetooth.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_bluetooth_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_bootstrap_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_clucene_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_concurrent.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_concurrent_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_core.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_core_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_dbus.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_dbus_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_designer.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_designer_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_designercomponents_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_gui.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_gui_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_help.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_help_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_labscontrols_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_labstemplates_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_location.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_location_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_multimedia.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_multimedia_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_multimediawidgets.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_network.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_network_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_nfc.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_nfc_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_opengl.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_opengl_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_openglextensions.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_openglextensions_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_platformsupport_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_positioning.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_positioning_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_printsupport.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_printsupport_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qml.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qml_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qmldevtools_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qmltest.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qmltest_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_quick.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_quick_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_quickparticles_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_quickwidgets.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_script.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_script_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_scripttools.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_scripttools_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_sensors.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_sensors_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_serialbus.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_serialbus_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_serialport.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_serialport_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_sql.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_sql_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_svg.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_svg_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_testlib.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_testlib_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_uiplugin.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_uitools.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_uitools_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_webchannel.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_webchannel_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_websockets.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_websockets_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_widgets.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_widgets_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_winextras.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_winextras_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_xml.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_xml_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_xmlpatterns.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_xmlpatterns_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\qt_functions.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\qt_config.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\win32\qt_config.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\win32-g++\qmake.conf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\spec_post.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\exclusive_builds.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\default_pre.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\win32\default_pre.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\resolve_config.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\exclusive_builds_post.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\default_post.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\build_pass.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\qml_debug.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\win32\rtti.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\precompile_header.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\warn_on.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\qt.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\resources.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\moc.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\win32\opengl.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\uic.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\file_copies.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\win32\windows.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\testcase_targets.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\exceptions.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\yacc.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\lex.prf ..\MonitorWhiteCtrlProgram.pro D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\qtmaind.prl D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\Qt5Widgetsd.prl D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\Qt5Guid.prl D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\Qt5Cored.prl    ..\DlgMain_MonitorWhiteCtrlProgram.h ..\EDIDManager.h ..\TrayManager.h ..\WhiteListManager.h ..\USBDeviceMonitor.h ..\USBScanWorker.h ..\ServiceManager.h ..\main.cpp ..\DlgMain_MonitorWhiteCtrlProgram.cpp ..\EDIDManager.cpp ..\TrayManager.cpp ..\WhiteListManager.cpp ..\USBDeviceMonitor.cpp ..\USBScanWorker.cpp ..\ServiceManager.cpp ..\DlgMain_MonitorWhiteCtrlProgram.ui    

clean: compiler_clean 
	-$(DEL_FILE) debug\main.o debug\DlgMain_MonitorWhiteCtrlProgram.o debug\EDIDManager.o debug\TrayManager.o debug\WhiteListManager.o debug\USBDeviceMonitor.o debug\USBScanWorker.o debug\ServiceManager.o debug\moc_DlgMain_MonitorWhiteCtrlProgram.o debug\moc_EDIDManager.o debug\moc_TrayManager.o debug\moc_WhiteListManager.o debug\moc_USBDeviceMonitor.o debug\moc_USBScanWorker.o debug\moc_ServiceManager.o

distclean: clean 
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_header_make_all: debug/moc_DlgMain_MonitorWhiteCtrlProgram.cpp debug/moc_EDIDManager.cpp debug/moc_TrayManager.cpp debug/moc_WhiteListManager.cpp debug/moc_USBDeviceMonitor.cpp debug/moc_USBScanWorker.cpp debug/moc_ServiceManager.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_DlgMain_MonitorWhiteCtrlProgram.cpp debug\moc_EDIDManager.cpp debug\moc_TrayManager.cpp debug\moc_WhiteListManager.cpp debug\moc_USBDeviceMonitor.cpp debug\moc_USBScanWorker.cpp debug\moc_ServiceManager.cpp
debug/moc_DlgMain_MonitorWhiteCtrlProgram.cpp: D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMainWindow \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmainwindow.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qline.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFile \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QProcessEnvironment \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocess.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextStream \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/QCloseEvent \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QEvent \
		../EDIDManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutexLocker \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QWaitCondition \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSize \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QByteArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextCodec \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextcodec.h \
		../DlgMain_MonitorWhiteCtrlProgram.h
	D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\moc.exe $(DEFINES) -D__GNUC__ -DWIN32 -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/mkspecs/win32-g++ -IE:/Work/002_Project/001_MonitorWhiteCtrlProgram/001_Code/001_MonitorWhiteCtrlProgram -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtANGLE -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore -I. ..\DlgMain_MonitorWhiteCtrlProgram.h -o debug\moc_DlgMain_MonitorWhiteCtrlProgram.cpp

debug/moc_EDIDManager.cpp: D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutexLocker \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QWaitCondition \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSize \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QByteArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextCodec \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextStream \
		../EDIDManager.h
	D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\moc.exe $(DEFINES) -D__GNUC__ -DWIN32 -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/mkspecs/win32-g++ -IE:/Work/002_Project/001_MonitorWhiteCtrlProgram/001_Code/001_MonitorWhiteCtrlProgram -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtANGLE -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore -I. ..\EDIDManager.h -o debug\moc_EDIDManager.cpp

debug/moc_TrayManager.cpp: D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QSystemTrayIcon \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsystemtrayicon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qline.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMenu \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTimer \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasictimer.h \
		../TrayManager.h
	D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\moc.exe $(DEFINES) -D__GNUC__ -DWIN32 -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/mkspecs/win32-g++ -IE:/Work/002_Project/001_MonitorWhiteCtrlProgram/001_Code/001_MonitorWhiteCtrlProgram -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtANGLE -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore -I. ..\TrayManager.h -o debug\moc_TrayManager.cpp

debug/moc_WhiteListManager.cpp: D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStringList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonDocument \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonarray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFile \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDir \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStandardPaths \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatetime.h \
		../EDIDManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutexLocker \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QWaitCondition \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSize \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QByteArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextCodec \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextStream \
		../WhiteListManager.h
	D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\moc.exe $(DEFINES) -D__GNUC__ -DWIN32 -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/mkspecs/win32-g++ -IE:/Work/002_Project/001_MonitorWhiteCtrlProgram/001_Code/001_MonitorWhiteCtrlProgram -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtANGLE -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore -I. ..\WhiteListManager.h -o debug\moc_WhiteListManager.cpp

debug/moc_USBDeviceMonitor.cpp: D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTimer \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStringList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStorageInfo \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFileSystemWatcher \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfilesystemwatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QWaitCondition \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qwaitcondition.h \
		../USBScanWorker.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDir \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFile \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QAbstractNativeEventFilter \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qabstractnativeeventfilter.h \
		../USBDeviceMonitor.h
	D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\moc.exe $(DEFINES) -D__GNUC__ -DWIN32 -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/mkspecs/win32-g++ -IE:/Work/002_Project/001_MonitorWhiteCtrlProgram/001_Code/001_MonitorWhiteCtrlProgram -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtANGLE -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore -I. ..\USBDeviceMonitor.h -o debug\moc_USBDeviceMonitor.cpp

debug/moc_USBScanWorker.cpp: D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTimer \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStringList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStorageInfo \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDir \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFile \
		../USBScanWorker.h
	D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\moc.exe $(DEFINES) -D__GNUC__ -DWIN32 -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/mkspecs/win32-g++ -IE:/Work/002_Project/001_MonitorWhiteCtrlProgram/001_Code/001_MonitorWhiteCtrlProgram -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtANGLE -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore -I. ..\USBScanWorker.h -o debug\moc_USBScanWorker.cpp

debug/moc_ServiceManager.cpp: D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QProcess \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocess.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSettings \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsettings.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QCoreApplication \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDir \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		../ServiceManager.h
	D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\moc.exe $(DEFINES) -D__GNUC__ -DWIN32 -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/mkspecs/win32-g++ -IE:/Work/002_Project/001_MonitorWhiteCtrlProgram/001_Code/001_MonitorWhiteCtrlProgram -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtANGLE -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore -I. ..\ServiceManager.h -o debug\moc_ServiceManager.cpp

compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_DlgMain_MonitorWhiteCtrlProgram.h
compiler_uic_clean:
	-$(DEL_FILE) ui_DlgMain_MonitorWhiteCtrlProgram.h
ui_DlgMain_MonitorWhiteCtrlProgram.h: ../DlgMain_MonitorWhiteCtrlProgram.ui
	D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\uic.exe ..\DlgMain_MonitorWhiteCtrlProgram.ui -o ui_DlgMain_MonitorWhiteCtrlProgram.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_header_clean compiler_uic_clean 



####### Compile

debug/main.o: ../main.cpp ../DlgMain_MonitorWhiteCtrlProgram.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMainWindow \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmainwindow.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qline.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFile \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QProcessEnvironment \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocess.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextStream \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/QCloseEvent \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QEvent \
		../EDIDManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutexLocker \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QWaitCondition \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSize \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QByteArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextCodec \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextcodec.h \
		../ServiceManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QProcess \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSettings \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsettings.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QCoreApplication \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDir \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStringList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdialog.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o ..\main.cpp

debug/DlgMain_MonitorWhiteCtrlProgram.o: ../DlgMain_MonitorWhiteCtrlProgram.cpp ../DlgMain_MonitorWhiteCtrlProgram.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMainWindow \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmainwindow.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qline.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFile \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QProcessEnvironment \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocess.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextStream \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/QCloseEvent \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QEvent \
		../EDIDManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutexLocker \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QWaitCondition \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSize \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QByteArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextCodec \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextcodec.h \
		ui_DlgMain_MonitorWhiteCtrlProgram.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QVariant \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QButtonGroup \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qbuttongroup.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QHeaderView \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qheaderview.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qabstractitemview.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qframe.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qstyleoption.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qabstractspinbox.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvalidator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qslider.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qabstractslider.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qstyle.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qtabbar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qrubberband.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QWidget \
		../TrayManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QSystemTrayIcon \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsystemtrayicon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMenu \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTimer \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasictimer.h \
		../WhiteListManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStringList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonDocument \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonarray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDir \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStandardPaths \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstandardpaths.h \
		../USBDeviceMonitor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStorageInfo \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFileSystemWatcher \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfilesystemwatcher.h \
		../USBScanWorker.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QAbstractNativeEventFilter \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qabstractnativeeventfilter.h \
		../ServiceManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QProcess \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSettings \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsettings.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QCoreApplication \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QInputDialog \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qinputdialog.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qlineedit.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtextcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtextformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFileInfo
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\DlgMain_MonitorWhiteCtrlProgram.o ..\DlgMain_MonitorWhiteCtrlProgram.cpp

debug/EDIDManager.o: ../EDIDManager.cpp ../EDIDManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutexLocker \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QWaitCondition \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSize \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QByteArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextCodec \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextStream
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\EDIDManager.o ..\EDIDManager.cpp

debug/TrayManager.o: ../TrayManager.cpp ../TrayManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QSystemTrayIcon \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsystemtrayicon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qline.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMenu \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTimer \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasictimer.h \
		../DlgMain_MonitorWhiteCtrlProgram.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMainWindow \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmainwindow.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFile \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QProcessEnvironment \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocess.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextStream \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/QCloseEvent \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QEvent \
		../EDIDManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutexLocker \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QWaitCondition \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSize \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QByteArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextCodec \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/QIcon \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/QPixmap \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/QPainter \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/QFont
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\TrayManager.o ..\TrayManager.cpp

debug/WhiteListManager.o: ../WhiteListManager.cpp ../WhiteListManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStringList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonDocument \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonarray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFile \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDir \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStandardPaths \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatetime.h \
		../EDIDManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutexLocker \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QWaitCondition \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qwaitcondition.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSize \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QByteArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextCodec \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextStream \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonParseError \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFileInfo \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStorageInfo \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDirIterator \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdiriterator.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\WhiteListManager.o ..\WhiteListManager.cpp

debug/USBDeviceMonitor.o: ../USBDeviceMonitor.cpp ../USBDeviceMonitor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTimer \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStringList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStorageInfo \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFileSystemWatcher \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfilesystemwatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QWaitCondition \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qwaitcondition.h \
		../USBScanWorker.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDir \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFile \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QAbstractNativeEventFilter \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qabstractnativeeventfilter.h \
		../WhiteListManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonDocument \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QJsonArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qjsonarray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStandardPaths \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatetime.h \
		../EDIDManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutexLocker \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSize \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QByteArray \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextCodec \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextcodec.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTextStream \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qline.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QWidget
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\USBDeviceMonitor.o ..\USBDeviceMonitor.cpp

debug/USBScanWorker.o: ../USBScanWorker.cpp ../USBScanWorker.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QTimer \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStringList \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QStorageInfo \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstorageinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QMutex \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDir \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFile \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\USBScanWorker.o ..\USBScanWorker.cpp

debug/ServiceManager.o: ../ServiceManager.cpp ../ServiceManager.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QObject \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QString \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QProcess \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocess.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QSettings \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsettings.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QCoreApplication \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDir \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QDebug \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qline.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QFileInfo \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/QThread \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qthread.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\ServiceManager.o ..\ServiceManager.cpp

debug/moc_DlgMain_MonitorWhiteCtrlProgram.o: debug/moc_DlgMain_MonitorWhiteCtrlProgram.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_DlgMain_MonitorWhiteCtrlProgram.o debug\moc_DlgMain_MonitorWhiteCtrlProgram.cpp

debug/moc_EDIDManager.o: debug/moc_EDIDManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_EDIDManager.o debug\moc_EDIDManager.cpp

debug/moc_TrayManager.o: debug/moc_TrayManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_TrayManager.o debug\moc_TrayManager.cpp

debug/moc_WhiteListManager.o: debug/moc_WhiteListManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_WhiteListManager.o debug\moc_WhiteListManager.cpp

debug/moc_USBDeviceMonitor.o: debug/moc_USBDeviceMonitor.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_USBDeviceMonitor.o debug\moc_USBDeviceMonitor.cpp

debug/moc_USBScanWorker.o: debug/moc_USBScanWorker.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_USBScanWorker.o debug\moc_USBScanWorker.cpp

debug/moc_ServiceManager.o: debug/moc_ServiceManager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_ServiceManager.o debug\moc_ServiceManager.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

